{"mcpServers": {"linear": {"command": "npx", "args": ["-y", "@tacticlaunch/mcp-linear"], "env": {"LINEAR_API_TOKEN": "************************************************"}}, "raindrop": {"command": "npx", "args": ["-y", "@adeze/raindrop-mcp@latest"], "env": {"RAINDROP_ACCESS_TOKEN": "e98ef0a7-d793-4776-b7f2-d370c7737964"}}, "notionApi": {"command": "npx", "args": ["-y", "@notionhq/notion-mcp-server@latest"], "env": {"OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer 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\", \"Notion-Version\": \"2022-06-28\"}"}}}}
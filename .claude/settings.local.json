{"permissions": {"allow": ["<PERSON><PERSON>(bin/rails runner:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(npm test:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(grep:*)", "Bash(bin/rails generate migration:*)", "Bash(bin/rails test:*)", "Bash(ls:*)", "Bash(npm run build:*)", "Bash(node:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(chmod:*)", "Bash(ruby test_spa_with_login.rb:*)", "Bash(bin/rails:*)", "Bash(cp:*)", "Bash(bundle install)", "Bash(bundle exec rspec:*)", "Bash(curl http://0.0.0.0:5100/*)", "Bash(curl -s http://localhost:5100/*)", "Bash(curl \"http://0.0.0.0:5100/*)", "Bash(curl -X GET http://0.0.0.0:5100/*)", "Bash(curl -X POST http://0.0.0.0:5100/*)", "Bash(curl -X PUT http://0.0.0.0:5100/*)", "Bash(curl -X DELETE http://0.0.0.0:5100/*)", "Bash(curl -X PATCH http://0.0.0.0:5100/*)", "Bash(redis-cli:*)", "<PERSON><PERSON>(curl:*)", "Bash(rails routes)", "mcp__gmail__gmail_send_email", "Bash(rails console:*)", "<PERSON><PERSON>(rails runner:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rg:*)", "Bash(bundle show:*)", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_click", "Bash(bundle config set:*)", "Bash(rails credentials:show:*)", "Bash(./scripts/rollback_jwt_migration.sh:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(rspec:*)", "Bash(bundle exec rails runner:*)", "Bash(bundle exec rails:*)", "Bash(ruby test:*)", "<PERSON><PERSON>(foreman start:*)", "Bash(kill:*)", "<PERSON><PERSON>(sed:*)", "Bash(git tag:*)", "Bash(ruby:*)", "Bash(rm:*)", "Bash(for file in meeting.html.erb welcome.html.erb tymlink.html.erb)", "Bash(do grep -n \"csrf_meta_tags\" \"/home/<USER>/Projects/attendifyapp/app/views/layouts/$file\")", "Bash(done)", "<PERSON><PERSON>(true)", "Bash(bundle exec ruby:*)", "<PERSON><PERSON>(mv:*)", "Bash(RAILS_ENV=test bundle exec rspec spec/channels/application_cable/connection_spec.rb)", "Bash(RAILS_ENV=test bundle exec rails console --brief)", "Bash(RAILS_ENV=test bundle exec rspec spec/channels/application_cable/connection_spec.rb:18 -v)", "Bash(rails:*)", "Bash(./test_jwt_logout_simple.sh:*)", "Bash(export:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(./test_jwt_invitation_flow.sh:*)", "mcp__gemini__analyze", "mcp__gemini__thinkdeep", "mcp__gemini__debug", "<PERSON><PERSON>(cat:*)", "mcp__gemini__codereview", "mcp__gemini__chat", "mcp__filesystem__create_directory", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(Event.event_types)", "Bash(npm info:*)", "mcp__postgres__query", "mcp__vue-mcp__get-component-tree", "Bash(HEADLESS=false bundle exec rspec spec/features/mobile_panel_debug_spec.rb --format documentation)", "mcp__filesystem__write_file", "<PERSON><PERSON>(env)", "mcp__linear__linear_search_issues", "mcp__linear__linear_create_issue", "mcp__linear__linear_getTeams", "mcp__linear__linear_getProjects", "mcp__linear__linear_createIssue", "mcp__linear__linear_addIssueToProject", "mcp__linear__linear_getLabels", "mcp__linear__linear_addIssueLabel", "<PERSON><PERSON>(gh issue edit:*)", "Bash(gh issue create:*)", "mcp__linear__linear_searchIssues", "mcp__linear__linear_getIssues", "mcp__linear__linear_getIssueById", "<PERSON><PERSON>(git worktree:*)", "Bash(git checkout:*)", "Bash(git commit:*)", "mcp__linear__linear_updateIssue", "mcp__linear__linear_getWorkflowStates", "WebFetch(domain:github.com)", "Bash(gh auth:*)", "Bash(gh api:*)", "mcp__linear__linear_createProject", "mcp__linear__linear_updateProject", "Bash(bundle check:*)", "Bash(git fetch:*)", "<PERSON><PERSON>(gh issue view:*)", "Bash(git log:*)", "Bash(git ls-tree:*)", "Bash(/tmp/comprehensive_auth_test.sh:*)", "Bash(git add:*)", "Bash(git push:*)", "<PERSON><PERSON>(git reset --hard)", "Bash(git clean -fd)", "Bash(git pull --ff-only)", "Bash(git pull:*)", "Bash(gh pr create:*)", "Bash(gh pr view:*)", "Bash(git stash push:*)", "mcp__linear__linear_getProjectIssues", "Bash(RAILS_ENV=test bundle exec rspec spec/requests/contracts_controller_spec.rb --format documentation)", "mcp__linear__linear_createComment", "<PERSON><PERSON>(convert:*)", "mcp__playwright-mcp__browser_navigate", "mcp__playwright-mcp__browser_type", "mcp__playwright-mcp__browser_click", "mcp__playwright-mcp__browser_wait_for", "mcp__playwright-mcp__browser_resize", "mcp__playwright-mcp__browser_take_screenshot", "mcp__playwright-mcp__browser_close", "mcp__playwright-mcp__browser_evaluate", "mcp__playwright-mcp__browser_snapshot", "mcp__playwright-mcp__browser_console_messages", "Bash(npm run dev:*)", "mcp__playwright-mcp__browser_select_option", "mcp__notionApi__API-post-search", "mcp__notionApi__API-get-self", "mcp__raindrop__diagnostics", "mcp__raindrop__collection_list", "mcp__gmail__gmail_list_emails", "mcp__gmail__gmail_read_email"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/home/<USER>/.claude"]}}
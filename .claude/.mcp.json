{"mcpServers": {"puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {"PUPPETEER_LAUNCH_OPTIONS": "{ \"headless\": true }", "ALLOW_DANGEROUS": "false"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/Projects/attendifyapp"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://localdeveloper:jednorozec@localhost/attendifyapp_development"]}, "gmail": {"command": "/home/<USER>/.npm-global/bin/mcp-gmail", "args": []}, "linear": {"command": "npx", "args": ["-y", "@tacticlaunch/mcp-linear"], "env": {"LINEAR_API_TOKEN": "************************************************"}}, "notionApi": {"command": "npx", "args": ["-y", "@notionhq/notion-mcp-server@latest"], "env": {"OPENAPI_MCP_HEADERS": "{\"Authorization\": \"Bearer 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\", \"Notion-Version\": \"2022-06-28\"}"}}}}